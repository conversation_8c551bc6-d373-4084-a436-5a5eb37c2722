name: OpenSSF Scorecard

on:
  branch_protection_rule:
  push:
    branches: [master]
  schedule:
    - cron: "30 0 * * 5"

permissions:
  contents: read

jobs:
  analyze:
    name: Analyze
    runs-on: ubuntu-latest

    permissions:
      actions: read
      contents: read
      id-token: write
      security-events: write

    steps:
      - name: Checkout repository
        uses: actions/checkout@08c6903cd8c0fde910a37f88322edcfb5dd907a8 # v5.0.0
        with:
          persist-credentials: false

      - name: Run analysis
        uses: ossf/scorecard-action@05b42c624433fc40578a4040d5cf5e36ddca8cde # v2.4.2
        with:
          results_file: results.sarif
          results_format: sarif
          publish_results: true

      - name: Upload results as artifact
        uses: actions/upload-artifact@ea165f8d65b6e75b540449e92b4886f43607fa02 # v4.6.2
        with:
          name: OpenSSF Scorecard results
          path: results.sarif
          retention-days: 5

      - name: Upload results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@96f518a34f7a870018057716cc4d7a5c014bd61c # v3.29.5
        with:
          sarif_file: results.sarif
