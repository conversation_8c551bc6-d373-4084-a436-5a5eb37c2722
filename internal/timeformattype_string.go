// Code generated by "stringer -type=TimeFormatType"; DO NOT EDIT.

package internal

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[TimeFormatNoTimezone-0]
	_ = x[TimeFormatNamedTimezone-1]
	_ = x[TimeFormatNumericTimezone-2]
	_ = x[TimeFormatNumericAndNamedTimezone-3]
	_ = x[TimeFormatTimeOnly-4]
}

const _TimeFormatType_name = "TimeFormatNoTimezoneTimeFormatNamedTimezoneTimeFormatNumericTimezoneTimeFormatNumericAndNamedTimezoneTimeFormatTimeOnly"

var _TimeFormatType_index = [...]uint8{0, 20, 43, 68, 101, 119}

func (i TimeFormatType) String() string {
	if i < 0 || i >= TimeFormatType(len(_TimeFormatType_index)-1) {
		return "TimeFormatType(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _TimeFormatType_name[_TimeFormatType_index[i]:_TimeFormatType_index[i+1]]
}
